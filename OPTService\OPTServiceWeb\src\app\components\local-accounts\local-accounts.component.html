<ng-container *ngIf="showContent">
    <div class="text-center" *ngIf="localAccountsData?.LocalAccountEnabled === false">
        <span>Local accounts is disabled</span>
    </div>
    <form [formGroup]="localAccountsForm" *ngIf="localAccountsData?.LocalAccountEnabled">
        <div id="card-new-customer" class="card form-group" *ngIf="disableModification === false">
            <div class="card-header d-flex justify-content-between">
                <button type="button" (click)="newCustomerCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>New customer</span>
                </button>
                <div id="newCustomerButtons" class="d-flex align-items-right">
                    <button id="newCustomerBtn" class="btn btn-primary" (click)="addNewCustomer($event)" [disabled]="disableModification || localAccountsForm.controls.newCustomer.invalid">
                        Create
                    </button>
                    <span id="newCustomerError" class="text-danger ml-2 pt-1" placement="right top" container="body" *ngIf="newCustomerError" ngbTooltip="Error creating new customer">
                        <i class="bi bi-exclamation-triangle"></i>
                    </span>
                </div>   
            </div>
            <div #newCustomerCollapse="ngbCollapse" [(ngbCollapse)]="collapseNewCustomer" class="card-body">

                <app-label-text id="newCustomerReference" labelColClass="col-3" textColClass="col-4" labelText="Customer reference" placeHolderText="Set customer reference"
                    controlName="CustomerReference" [formGroup]="localAccountsForm.controls.newCustomer" maxLength="15">
                </app-label-text>

                <app-label-text id="newCustomerName" labelColClass="col-3" textColClass="col-4" labelText="Name" placeHolderText="Set name"
                    controlName="Name" [formGroup]="localAccountsForm.controls.newCustomer" maxLength="15">
                </app-label-text>

                <app-switch-label id="newCustomerTransactionsAllowed" labelText="Transactions allowed" controlName="TransactionsAllowed"
                    [formGroup]="localAccountsForm.controls.newCustomer">
                </app-switch-label>

                <app-label-text id="newCustomerTransactionLimit" labelColClass="col-3" textColClass="col-4" labelText="Transaction limit" placeHolderText="0"
                    controlName="TransactionLimit" [formGroup]="localAccountsForm.controls.newCustomer" maxLength="15" appendText="p">
                </app-label-text>

                <app-switch-label id="newCustomerPin" labelText="Pin" controlName="Pin"
                    [formGroup]="localAccountsForm.controls.newCustomer">
                </app-switch-label>

                <app-switch-label id="newCustomerPrintValue" labelText="Print value" controlName="PrintValue"
                    [formGroup]="localAccountsForm.controls.newCustomer">
                </app-switch-label>

                <app-switch-label id="newCustomerAllowLoyalty" labelText="Allow loyalty" controlName="AllowLoyalty"
                    [formGroup]="localAccountsForm.controls.newCustomer">
                </app-switch-label>

                <app-switch-label id="newCustomerFuelOnly" labelText="Fuel only" controlName="FuelOnly"
                    [formGroup]="localAccountsForm.controls.newCustomer">
                </app-switch-label>

                <app-switch-label id="newCustomerRegistrationEntry" labelText="Registration entry" controlName="RegistrationEntry"
                    [formGroup]="localAccountsForm.controls.newCustomer">
                </app-switch-label>

                <app-switch-label id="newCustomerMileageEntry" labelText="Mileage entry" controlName="MileageEntry"
                    [formGroup]="localAccountsForm.controls.newCustomer">
                </app-switch-label>

                <div class="row form-group">
                    <label class="col-3 col-form-label">Credit status</label>
                    <div ngbDropdown class="col-4 d-inline-block">
                        <button class="btn btn-outline-primary" id="dropdownNewCustomerStatus" ngbDropdownToggle>{{getNewCustomerCreditStatus()}}</button>
                        <div ngbDropdownMenu aria-labelledby="dropdownNewCustomerStatus">
                            <button ngbDropdownItem (click)="setNewCustomerCreditStatus('PrePayAccount')">Pre pay account</button>
                            <button ngbDropdownItem (click)="setNewCustomerCreditStatus('LowCreditWarning')">Low credit warning</button>
                            <button ngbDropdownItem (click)="setNewCustomerCreditStatus('MaxCreditReached')">Maximum credit reached</button>
                            <button ngbDropdownItem (click)="setNewCustomerCreditStatus('None')">None</button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="text-center" *ngIf="localAccountsData.LocalAccountCustomers === null || localAccountsData.LocalAccountCustomers.length === 0">
            <span>No customers</span>
        </div>
        <div *ngFor="let customer of localAccountsData.LocalAccountCustomers; let i = index">
            <div [id]="'card-customer'+i" class="card form-group card-customer">
                <div class="card-header d-flex justify-content-between">
                    <button type="button" (click)="customerCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                        <span>Customer reference {{customer.CustomerReference}} - {{customer.Name}} - Balance: £{{customer.Balance}}</span>
                    </button>
                    <div class="customerButtons">
                        <button id="removeCustomerBtn{{i}}" class="btn btn-primary" (click)="removeCustomer(i, 'customer', $event)" [disabled]="disableModification" *ngIf="disableModification === false">Remove</button>
                        <span id="removeCustomerError{{i}}" class="text-danger ml-2 pt-1" placement="right top" container="body" *ngIf="errors[i]?.customer"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>                
                </div>
                <div #customerCollapse="ngbCollapse" [(ngbCollapse)]="collapseMap.get(customer.CustomerReference)?.collapse" class="card-body">
                     
                    <app-label-text-button [id]="'name'+i" labelColClass="col-12 col-md-2 col-xl-2"
                        textColClass="col-12 col-md-6 col-xl-3" labelText="Name" controlName="Name"
                        [errorInAction]="errors[i]?.name" (action)="addCustomer(i, 'name', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        maxLength="10" [disabled]="disableModification">
                    </app-label-text-button>

                    <app-switch-label [id]="'transactionsAllowed'+i" labelText="Transactions allowed" controlName="TransactionsAllowed"
                        [errorInAction]="errors[i]?.transactionsAllowed" (action)="addCustomer(i, 'transactionsAllowed', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        [disabled]="disableModification">
                    </app-switch-label>

                    <app-label-text-button [id]="'transactionLimit'+i" labelColClass="col-12 col-md-2 col-xl-2"
                        textColClass="col-12 col-md-6 col-xl-3" labelText="Transaction limit" controlName="TransactionLimit"
                        [errorInAction]="errors[i]?.transactionLimit" (action)="addCustomer(i, 'transactionLimit', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        maxLength="10" appendText="p" [disabled]="disableModification">
                    </app-label-text-button>

                    <app-switch-label [id]="'pin'+i" labelText="Pin" controlName="Pin"
                        [errorInAction]="errors[i]?.pin" (action)="addCustomer(i, 'pin', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        [disabled]="disableModification">
                    </app-switch-label>

                    <app-switch-label [id]="'printValue'+i" labelText="Print value" controlName="PrintValue"
                        [errorInAction]="errors[i]?.printValue" (action)="addCustomer(i, 'printValue', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        [disabled]="disableModification">
                    </app-switch-label>

                    <app-switch-label [id]="'allowLoyalty'+i" labelText="Allow loyalty" controlName="AllowLoyalty"
                        [errorInAction]="errors[i]?.allowLoyalty" (action)="addCustomer(i, 'allowLoyalty', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        [disabled]="disableModification">
                    </app-switch-label>
                    
                    <app-switch-label [id]="'fuelOnly'+i" labelText="Fuel only" controlName="FuelOnly"
                        [errorInAction]="errors[i]?.fuelOnly" (action)="addCustomer(i, 'fuelOnly', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        [disabled]="disableModification">
                    </app-switch-label>
                    
                    <app-switch-label [id]="'registrationEntry'+i" labelText="Registration entry" controlName="RegistrationEntry"
                        [errorInAction]="errors[i]?.registrationEntry" (action)="addCustomer(i, 'registrationEntry', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        [disabled]="disableModification">
                    </app-switch-label>
                    
                    <app-switch-label [id]="'mileageEntry'+i" labelText="Mileage entry" controlName="MileageEntry"
                        [errorInAction]="errors[i]?.mileageEntry" (action)="addCustomer(i, 'mileageEntry', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        [disabled]="disableModification">
                    </app-switch-label>

                    <div class="row form-group">
                        <label class="col-12 col-md-2 col-xl-2 col-form-label" for="dropdownStatus{{i}}">Credit status</label>
                        <div ngbDropdown class="col-12 col-md-6 col-xl-3 d-inline-block">
                            <button class="btn btn-outline-primary" id="dropdownStatus{{i}}" ngbDropdownToggle [disabled]="disableModification">{{getCreditStatus(i)}}</button>
                            <div ngbDropdownMenu aria-labelledby="dropdownStatus{{i}}">
                                <button ngbDropdownItem (click)="setCreditStatus(i, 'PrePayAccount')">Pre pay account</button>
                                <button ngbDropdownItem (click)="setCreditStatus(i, 'LowCreditWarning')">Low credit warning</button>
                                <button ngbDropdownItem (click)="setCreditStatus(i, 'MaxCreditReached')">Maximum credit reached</button>
                                <button ngbDropdownItem (click)="setCreditStatus(i, 'None')">None</button>
                            </div>
                            <button id="saveStatusBtn{{i}}" class="btn btn-primary ml-2" (click)="addCustomer(i, 'creditStatus', $event)" [disabled]="disableModification">Save</button>
                            <span id="saveStatuError{{i}}" class="text-danger ml-2 pt-1" placement="right top" container="body" *ngIf="errors[i]?.creditStatus"><i class="bi bi-exclamation-triangle"></i></span>
                        </div>
                    </div>

                    <app-label-text-button [id]="'balance'+i" labelColClass="col-12 col-md-2 col-xl-2"
                        textColClass="col-12 col-md-6 col-xl-3" labelText="Balance" controlName="Balance"
                        [errorInAction]="errors[i]?.balance" (action)="setCustomerBalance(i, 'balance', $event)" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]"
                        maxLength="10" [disabled]="disableModification">
                    </app-label-text-button>

                    <div class="card form-group card-customer-cards">
                        <div class="card-header">
                            <button type="button" (click)="cardsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                                <span>Cards</span>
                            </button>
                        </div>
                        <div #cardsCollapse="ngbCollapse" [(ngbCollapse)]="collapseMap.get(customer.CustomerReference)?.cards" class="card-body">
                            <div class="text-center mb-2" *ngIf="customer.Cards === null || customer.Cards.length === 0">
                                <span>No cards</span>
                            </div>
                            <div class="card form-group" *ngIf="disableModification === false">
                                <div class="card-header d-flex justify-content-between">                                    
                                    <label class="col-form-label" for="newCardBtn{{i}}">New card</label>
                                    <div class="newCardButtons">
                                        <button id="newCardBtn{{i}}" class="btn btn-primary" (click)="addNewCard(i, 'add', $event)" [disabled]="disableModification">
                                            Add
                                        </button>
                                        <span id="newCardError{{i}}" class="text-danger ml-2 pt-1" placement="right top" container="body" *ngIf="errors[i]?.add" ngbTooltip="Error adding new card">
                                            <i class="bi bi-exclamation-triangle"></i>
                                        </span>
                                    </div>                                    
                                </div>
                                <div class="card-body"> 
                                    <app-label-text [id]="'cardPan'+i" labelColClass="col-3" textColClass="col-4" labelText="PAN" placeHolderText="Set PAN"
                                        controlName="CardPan" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]" maxLength="15">
                                    </app-label-text>
                                    <app-label-text [id]="'cardDescription'+i" labelColClass="col-3" textColClass="col-4" labelText="Description" placeHolderText="Set description"
                                        controlName="CardDescription" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]" maxLength="15">
                                    </app-label-text>
                                    <app-label-text [id]="'cardDiscount'+i" labelColClass="col-3" textColClass="col-4" labelText="Discount"
                                        controlName="CardDiscount" [formGroup]="localAccountsForm.controls.localAccountCustomers.controls[i]" maxLength="15">
                                    </app-label-text>
                                </div>                                
                            </div>                
                            <div *ngFor="let card of customer.Cards; let j = index">                                
                                <app-local-account-card [form]="localAccountsForm.controls.localAccountCustomers.controls[i].controls.Cards.controls[j]" [card]="card"
                                    [customer]="customer" [collapse]="collapseMap.get(customer.CustomerReference).cardList?.get(card.Pan)?.collapse" 
                                    (collapseChanged)="collapseChanged(customer.CustomerReference, card.Pan)" id="Customer{{i}}Card{{j}}"
                                    [disabled]="disableModification">
                                </app-local-account-card>
                            </div>
                        </div>
                    </div>
                </div>    
            </div>
        </div>
    </form>
</ng-container>